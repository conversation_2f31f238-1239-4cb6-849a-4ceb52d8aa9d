[package]

# Note: Semantic Versioning is used: https://semver.org/
version = "0.40.22"

# Description
title = "Isaac Lab framework for Robot Learning"
description="Extension providing main framework interfaces and abstractions for robot learning."
readme  = "docs/README.md"
repository = "https://github.com/isaac-sim/IsaacLab"
category = "robotics"
keywords = ["kit", "robotics", "learning", "ai"]

[python.pipapi]
requirements = [
    "numpy",
    "prettytable==3.3.0",
    "toml",
    "hidapi",
    "gymnasium==0.29.0",
    "trimesh"
]

modules = [
    "numpy",
    "prettytable",
    "toml",
    "hid",
    "gymnasium",
    "trimesh"
]

use_online_index=true

[core]
reloadable = false

[[python.module]]
name = "isaaclab"
